"use client";

import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  <PERSON>alogTitle,
  DialogDescription,
  DialogTrigger,
  DialogClose,
  DialogFooter,
} from "@/components/ui/dialog";
import { Image, Section } from "@prisma/client";
import Form from "next/form";
import { updateImageAction } from "@/actions/image";
import { toast } from "sonner";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { FormMessage } from "@/components/ui/form";

type Props = {
  children?: React.ReactNode;
  sectionId: Section["id"];
  image?: Image;
};

const ImageForm = ({ sectionId, image, children }: Props) => {
  const [src, setSrc] = useState(image?.src || "");
  const [alt, setAlt] = useState(image?.alt || "");
  const [caption, setCaption] = useState(image?.caption || "");
  const [width, setWidth] = useState(image?.width || "");
  const [height, setHeight] = useState(image?.height || "");

  const handleUpdateImage = async () => {
    const { data, message, error } = await updateImageAction({
      id: image?.id as string,
      src,
      alt,
      caption,
      width,
      height,
    });

    if (data) {
      toast.success(message);
    }

    if (error) {
      toast.error(message);
    }
  };

  return (
    <>
      <Dialog>
        <DialogTrigger asChild>{children}</DialogTrigger>
        <DialogContent className="min-w-3xl max-w-4xl">
          <DialogHeader>
            <DialogTitle>Change Image</DialogTitle>
            <DialogDescription>
              Update the image URL, alt, caption, width, and height.
            </DialogDescription>
          </DialogHeader>
          <Form className="space-y-3" action={handleUpdateImage}>
            <div className="space-y-2">
              <Label htmlFor="src">Image Source URL</Label>
              <Textarea
                id="src"
                placeholder="Image Source URL"
                className="peer text-xs"
                value={src}
                onChange={(e) => setSrc(e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="alt">Image Alt</Label>
              <Textarea
                id="alt"
                placeholder="Image Alt"
                className="peer text-xs"
                value={alt}
                onChange={(e) => setAlt(e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="caption">Image Caption</Label>
              <Textarea
                id="caption"
                placeholder="Image Caption"
                className="peer text-xs"
                value={caption}
                onChange={(e) => setCaption(e.target.value)}
              />
            </div>
            <div className="space-y-2 w-1/2">
              <Label htmlFor="width">Image Width</Label>
              <Input
                type="number"
                id="width"
                placeholder="Image Width"
                className="peer text-xs w-1/2"
                value={width}
                onChange={(e) => setWidth(e.target.value)}
              />
              <FormMessage> Image width in pixels </FormMessage>
            </div>
            <div className="space-y-2 w-1/2">
              <Label htmlFor="height">Image Height</Label>
              <Input
                type="number"
                id="height"
                placeholder="Image Height"
                className="peer text-xs w-1/2"
                value={height}
                onChange={(e) => setHeight(e.target.value)}
              />
              <FormMessage> Image height in pixels </FormMessage>
            </div>

            <Button type="submit">Update</Button>
          </Form>
          <DialogFooter>
            <DialogClose asChild>
              <Button variant="outline" type="button">
                Cancel
              </Button>
            </DialogClose>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default ImageForm;
