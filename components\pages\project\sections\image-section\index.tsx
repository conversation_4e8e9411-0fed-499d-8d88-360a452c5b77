import React from "react";
import Image from "next/image";
import { Section, Image as Images } from "@prisma/client";
import { Button } from "@/components/ui/button";
import ImageForm from "./image-form";

type Props = {
  section: Section & {
    image?: Images | null;
  };
};

const ImageSection = ({ section }: Props) => {
  if (!section.image) {
    return null;
  }

  return (
    <figure className="relative flex flex-col items-center group/section">
      <Image
        src={section.image?.src as string}
        alt={section.image?.alt as string}
        width={500}
        height={500}
        className="w-64 h-64 object-cover"
      />

      {section.image?.caption && (
        <figcaption className="text-sm text-muted-foreground mt-8">
          {section.image?.caption}
        </figcaption>
      )}

      {/* Overlay on hover */}
      <ImageForm sectionId={section.id} image={section.image}>
        <div className="absolute inset-0 hover:bg-black/40 group-hover/section:opacity-100 transition-colors duration-300 flex items-center justify-center">
          <Button
            variant="secondary"
            size="sm"
            className="opacity-0 group-hover:opacity-100 transition-opacity duration-300 gap-2"
          >
            Change Image
          </Button>
        </div>
      </ImageForm>
    </figure>
  );
};

export default ImageSection;
