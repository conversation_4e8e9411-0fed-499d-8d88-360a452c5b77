"use server";

import { ActionState, StatusCode } from "@/lib/types";
import { createText, isText, updateText } from "@/queries/text";
import { revalidatePath } from "next/cache";
import { Text } from "@prisma/client";

type CreateTextProps = {
  sectionId: Text["sectionId"];
  content?: Text["content"];
  rowPosition?: Text["rowPosition"];
};

export const createTextAction = async ({
  sectionId,
  content,
}: CreateTextProps): Promise<ActionState<Text>> => {
  let text;
  try {
    text = await createText({ sectionId, content });
    return {
      code: StatusCode.Created,
      message: "Text created successfully",
      data: text as Text,
      success: true,
    };
  } catch (error) {
    console.error("Error creating text:", error);
    return {
      code: StatusCode.InternalServerError,
      message: "Something went wrong while creating the text",
      error: error as Error,
      success: false,
    };
  } finally {
    revalidatePath(`/project/${text?.section?.projectId}`);
  }
};

export const updateTextAction = async ({
  id,
  content,
}: {
  id: Text["id"];
  content: Text["content"];
  rowPosition?: Text["rowPosition"];
}): Promise<ActionState<Text>> => {
  let text;
  try {
    const isTextExists = await isText(id);

    if (!isTextExists) {
      return {
        code: StatusCode.NotFound,
        message: "Text not found",
        success: false,
      };
    }

    text = await updateText({ id, content, rowPosition });
    return {
      code: StatusCode.Ok,
      message: "Text updated successfully",
      data: text as Text,
      success: true,
    };
  } catch (error) {
    console.error("Error updating text:", error);
    return {
      code: StatusCode.InternalServerError,
      message: "Something went wrong while updating the text",
      error: error as Error,
      success: false,
    };
  } finally {
    revalidatePath(`/project/${text?.section?.projectId}`);
  }
};
