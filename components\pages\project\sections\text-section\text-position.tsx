import React, { Children } from "react";
import { Button } from "@/components/ui/button";
import { ArrowLeft, ArrowRight } from "lucide-react";
import { cn } from "@/lib/utils";
import { Direction } from "@/lib/utils2";

type Props = {
  children?: React.ReactNode;
  onPositionChange: (direction: Direction) => void;
};

export const POSITION_BUTTON = [
  {
    icon: <ArrowLeft className="h-4 w-4" />,
    position: Direction.LEFT,
    className: "left-0 group-hover:-translate-x-12",
  },
  {
    icon: <ArrowRight className="h-4 w-4" />,
    position: Direction.RIGHT,
    className: "right-0 group-hover:translate-x-12",
  },
];

const TextPosition = ({ onPositionChange }: Props) => {
  return (
    <div className="">
      {POSITION_BUTTON.map(({ icon, position, className }) => (
        <Button
          key={position}
          className={cn(
            "absolute top-1/2 -translate-y-1/2",
            className,
            " invisible group-hover:visible"
          )}
          size="icon"
          onClick={(event) => {
            event.stopPropagation();
            onPositionChange(position);
          }}
        >
          {icon}
        </Button>
      ))}
    </div>
  );
};

export default TextPosition;
